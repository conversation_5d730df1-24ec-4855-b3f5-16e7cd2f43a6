package com.think1024.tocodesign.ideaplugin.intellimerge.util;

import com.think1024.tocodesign.ideaplugin.intellimerge.model.mapping.EdgeLabel;
import java.io.*;
import java.util.regex.Pattern;

public class Utils {

  public static final String CONFLICT_LEFT_BEGIN = "<<<<<<<";
  public static final String CONFLICT_BASE_BEGIN = "|||||||";
  public static final String CONFLICT_RIGHT_BEGIN = "=======";
  public static final String CONFLICT_RIGHT_END = ">>>>>>>";


  /**
   * Format separator in path to jgit style "/"
   *
   * @param path
   * @return
   */
  public static String formatPathSeparator(String path) {
    return path.replaceAll(Pattern.quote(File.separator), "/").replaceAll("/+", "/");
  }

  public static Integer getEdgeLabelIndex(String edgeLabel) {
    for (EdgeLabel label : EdgeLabel.values()) {
      if (label.name().equals(edgeLabel)) {
        return label.getIndex();
      }
    }
    return -1;
  }
}
