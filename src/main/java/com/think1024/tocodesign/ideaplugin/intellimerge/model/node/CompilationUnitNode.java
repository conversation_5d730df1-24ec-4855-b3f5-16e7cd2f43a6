package com.think1024.tocodesign.ideaplugin.intellimerge.model.node;

import com.think1024.tocodesign.ideaplugin.intellimerge.model.constant.NodeType;

import java.util.Set;

public class CompilationUnitNode extends CompositeNode {
  private String packageStatement;
  private Set<String> importStatements; // ordered, so use LinkedHashSet
  private String path;
  //  private CompilationUnit cu; // corresponding AST node, to get package and import contents in
  // merging

  public CompilationUnitNode(
      Integer nodeID,
      Boolean needToMerge,
      NodeType nodeType,
      String displayName,
      String qualifiedName,
      String originalSignature,
      String comment,
      String path,
      String packageStatement,
      Set<String> importStatements) {
    super(nodeID, needToMerge, nodeType, displayName, qualifiedName, originalSignature, comment);
    this.path = path;
    this.packageStatement = packageStatement;
    this.importStatements = importStatements;
  }

  public String getPackageStatement() {
    return packageStatement;
  }

  public void setPackageStatement(String packageStatement) {
    this.packageStatement = packageStatement;
  }

  public Set<String> getImportStatements() {
    return importStatements;
  }

  public void setImportStatements(Set<String> importStatements) {
    this.importStatements = importStatements;
  }

  public String getPath() {
    return path;
  }
}
