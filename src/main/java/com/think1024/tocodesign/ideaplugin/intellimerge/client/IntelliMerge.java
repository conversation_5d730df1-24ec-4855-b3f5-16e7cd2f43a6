package com.think1024.tocodesign.ideaplugin.intellimerge.client;

import com.github.javaparser.JavaParser;
import com.google.common.base.Stopwatch;
import com.think1024.tocodesign.ideaplugin.intellimerge.core.GraphBuilderV2;
import com.think1024.tocodesign.ideaplugin.intellimerge.core.GraphMergeResult;
import com.think1024.tocodesign.ideaplugin.intellimerge.core.GraphMerger;
import com.think1024.tocodesign.ideaplugin.intellimerge.model.SemanticEdge;
import com.think1024.tocodesign.ideaplugin.intellimerge.model.SemanticNode;
import com.think1024.tocodesign.ideaplugin.intellimerge.model.constant.Side;
import org.apache.commons.lang3.tuple.Pair;
import org.jgrapht.Graph;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/** The class is responsible to provide CLI and API service to users */
public class IntelliMerge {
//  private static final Logger logger = LoggerFactory.getLogger(IntelliMerge.class);

  public IntelliMerge() {
    JavaParser.getStaticConfiguration().setAttributeComments(true);
  }

  /**
   * Merge directories in the order <left> <base> <right> and return time cost for each phase
   *
   * @param ours
   * @param base
   * @param theirs
   * @return
   * @throws Exception
   */
  public List<Pair<String, GraphMergeResult>> merge(
      List<Pair<String, String>> ours,
      List<Pair<String, String>> base,
      List<Pair<String, String>> theirs,
      Boolean returnBaseWhenConflict
  ) throws Exception {

    ExecutorService executorService = Executors.newFixedThreadPool(3);

    // 1. Build graphs from given directories
    Future<Graph<SemanticNode, SemanticEdge>> oursBuilder =
        executorService.submit(new GraphBuilderV2(ours, Side.OURS));
    Future<Graph<SemanticNode, SemanticEdge>> baseBuilder =
        executorService.submit(new GraphBuilderV2(base, Side.BASE));
    Future<Graph<SemanticNode, SemanticEdge>> theirsBuilder =
        executorService.submit(
            new GraphBuilderV2(theirs, Side.THEIRS));

    Stopwatch stopwatch = Stopwatch.createStarted();
    Graph<SemanticNode, SemanticEdge> oursGraph = oursBuilder.get();
    Graph<SemanticNode, SemanticEdge> baseGraph = baseBuilder.get();
    Graph<SemanticNode, SemanticEdge> theirsGraph = theirsBuilder.get();

    stopwatch.stop();
    executorService.shutdown();
    long buildingTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//    logger.info("({}ms) Done building graphs.", buildingTime);

    GraphMerger merger = new GraphMerger(oursGraph, baseGraph, theirsGraph, returnBaseWhenConflict);

    // 2. Match nodes across the 3-way graphs.
    stopwatch.reset().start();
//    Pair<List<Refactoring>, List<Refactoring>> refactorings = merger.threewayMap();
    merger.threewayMap();
    stopwatch.stop();
    long matchingTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//    logger.info("({}ms) Done matching graphs.", matchingTime);

    // save the detected refactorings into csv for human validation and debugging
//    String b2oCsvFilePath = outputPath + File.separator + "ours_refactorings.csv";
//    String b2tCsvFilePath = outputPath + File.separator + "theirs_refactorings.csv";
//    saveRefactorings(b2oCsvFilePath, refactorings.getLeft());
//    saveRefactorings(b2tCsvFilePath, refactorings.getRight());

    // 3. Merge programs with the 3-way graphs, keeping the original format and directory structure
    stopwatch.reset().start();
//    List<String> mergedFilePaths = merger.threewayMerge();
    List<Pair<String, GraphMergeResult>> results = merger.threewayMerge();
    stopwatch.stop();
    long mergingTime = stopwatch.elapsed(TimeUnit.MILLISECONDS);
//    logger.info("({}ms) Done merging programs.", mergingTime);

//    long overall = buildingTime + matchingTime + mergingTime;
//    logger.info("Merged {} files. Overall time cost: {}ms.", mergedFilePaths.size(), overall);

//    List<Long> runtimes = new ArrayList<>();
//    runtimes.add(buildingTime);
//    runtimes.add(matchingTime);
//    runtimes.add(mergingTime);
//    runtimes.add(overall);
    return results;
  }
}
