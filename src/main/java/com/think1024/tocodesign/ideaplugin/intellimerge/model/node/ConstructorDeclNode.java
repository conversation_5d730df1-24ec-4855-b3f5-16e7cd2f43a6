package com.think1024.tocodesign.ideaplugin.intellimerge.model.node;

import com.github.javaparser.Range;
import com.think1024.tocodesign.ideaplugin.intellimerge.model.constant.EdgeType;
import com.think1024.tocodesign.ideaplugin.intellimerge.model.constant.NodeType;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/** access == public, no return type */
public class ConstructorDeclNode extends TerminalNode {
  private String constructorName; // signature

  public ConstructorDeclNode(
      Integer nodeID,
      Boolean needToMerge,
      NodeType nodeType,
      String displayName,
      String qualifiedName,
      String originalSignature,
      String comment,
      List<String> annotations,
      List<String> modifiers,
      String constructorName,
      String body,
      Optional<Range> range) {
    super(
        nodeID,
        needToMerge,
        nodeType,
        displayName,
        qualifiedName,
        originalSignature,
        comment,
        annotations,modifiers,
        body,
        range);
    this.constructorName = constructorName;
  }
}
