package com.think1024.tocodesign.ideaplugin.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Paths
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors
import kotlin.random.Random


enum class FileType {
    FILE, DIRECTORY
}

object FileUtil {
    private val logger = Logger.getInstance(FileUtil::class.java)
    /**
     * Checks if a file is a system file like .DS_Store or Thumbs.db
     */
    private fun isDirectoryOsFile(filepath: String): Boolean {
        return filepath.endsWith(".DS_Store") || filepath.endsWith("Thumbs.db")
    }

    private  fun getFileType(path: String): FileType {
        return if (Files.isDirectory(Paths.get(path))) FileType.DIRECTORY else FileType.FILE
    }

    private fun readDirectory(path: String): List<Pair<String, FileType>> {
        return try {
            Files.list(Paths.get(path))
                .map { file ->
                    val relativePath = file.fileName.toString()
                    val type = if (Files.isDirectory(file)) FileType.DIRECTORY else FileType.FILE
                    relativePath to type
                }
                .collect(Collectors.toList())
        } catch (e: Exception) {
            emptyList()
        }
    }

    fun readDirectoryRecursive(path: String, type: FileType? = null): List<Pair<String, FileType>> {
        return try {
            if (path.isEmpty()) {
                return emptyList()
            }

            val filePath = Paths.get(path)
            if (!Files.exists(filePath)) {
                return emptyList()
            }

            // Skip if path is a special directory file
            if (isDirectoryOsFile(path)) {
                return emptyList()
            }

            // Determine file type if not provided
            val resolvedType = type ?: getFileType(path)
            val result = mutableListOf<Pair<String, FileType>>()

            when (resolvedType) {
                FileType.FILE -> {
                    result.add(path to FileType.FILE)
                }
                FileType.DIRECTORY -> {
                    readDirectory(path).forEach { (subPath, subType) ->
                        val filepath = Paths.get(path, subPath).toString()
                        val subResult = readDirectoryRecursive(filepath, subType)
                        result.addAll(subResult)
                    }
                }
            }

            result
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Checks if a directory is empty, excluding specified files and isDirectoryOsFile
     */
    fun isEmptyDirectory(filepath: String, exclude: List<String> = emptyList()): Boolean {
        val dirPath = Paths.get(filepath)

        // 检查是否为目录
        if (!Files.isDirectory(dirPath)) {
            return false
        }

        return try {
            Files.list(dirPath).use { stream ->
                // 遍历目录条目，检查是否存在未排除的文件
                stream.noneMatch { entry ->
                    val filename = entry.fileName.toString()
                    // 判断是否被排除（基于文件名）或是否为系统文件（基于完整路径）
                    exclude.contains(filename) || isDirectoryOsFile(entry.toString())
                }
            }
        } catch (e: IOException) {
            // 处理访问异常（如权限不足）
            false
        }
    }

    private fun normalizePath(path: String): String? {
        val cleanPath = FileUtil.toSystemIndependentName(path.trim())
        if (cleanPath.isBlank()) return null
        return cleanPath
    }

    fun findVirtualFile(path: String): VirtualFile? {
        var file = LocalFileSystem.getInstance().findFileByPath(path)
        if (file == null) {
            if (ApplicationManager.getApplication().isDispatchThread) {
                file = LocalFileSystem.getInstance().refreshAndFindFileByPath(path)
            } else {
                ApplicationManager.getApplication().runWriteAction {
                    file = LocalFileSystem.getInstance().refreshAndFindFileByPath(path)
                }
            }
        }
        return file
    }

    fun getVirtualFile(path: String): VirtualFile? {
        val cleanPath = normalizePath(path) ?: return null

        return try {
            val file = findVirtualFile(cleanPath)
            return file.takeIf { it?.exists() === true }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取虚拟文件，支持相对于项目的路径
     *
     * @param path 文件路径，可以是绝对路径或相对于项目的路径
     * @param project 项目实例，用于解析相对路径
     * @return 虚拟文件对象，如果找不到则返回null
     */
    fun getVirtualFile(path: String, project: Project?): VirtualFile? {
        val cleanPath = normalizePath(path) ?: return null

        // 判断是否为绝对路径
        val isAbsolutePath = Paths.get(cleanPath).isAbsolute
        if (isAbsolutePath) {
            // 直接尝试获取绝对路径的文件
            return getVirtualFile(cleanPath)
        } else if (project != null) {
            // 作为相对路径处理
            val basePath = project.basePath
            if (basePath != null) {
                val absolutePath = Paths.get(basePath, cleanPath).toString()
                return getVirtualFile(absolutePath)
            }
        }
        // 作为备选方案，尝试直接获取（可能是相对于当前工作目录的路径）
        return getVirtualFile(cleanPath)
    }

    fun getFile(path: String, create: Boolean = false): File? {
        val cleanPath = normalizePath(path) ?: return null
        return try {
            var file = File(cleanPath)
            if (create && !file.exists()) {
                val path = Paths.get(cleanPath)
                val parentDirPath = path.parent
                if (parentDirPath != null && !Files.exists(parentDirPath)) {
                    Files.createDirectories(parentDirPath)
                }
                Files.createFile(path)
                file = File(cleanPath)
            }
            file.takeIf { it.exists() }
        } catch (e: Exception) {
            null
        }
    }

    fun normalizeAbsolutePath(path: String, project: Project? = null): String? {
        val cleanPath = normalizePath(path) ?: return null

        // 判断是否为绝对路径
        val isAbsolutePath = Paths.get(cleanPath).isAbsolute
        if (isAbsolutePath) {
            return cleanPath
        } else if (project != null) {
            // 作为相对路径处理
            val basePath = project.basePath
            if (basePath != null) {
                return Paths.get(basePath, cleanPath).toString()
            }
        }
        // 作为备选方案，尝试直接获取（可能是相对于当前工作目录的路径）
        return cleanPath
    }

    /**
     * 用FileIO判断文件是否存在
     */
    fun fileExists(filePath: String, project: Project): Boolean {
        // 首先规范化路径，处理相对路径
        val absolutePath = normalizeAbsolutePath(filePath, project) ?: return false

        // 第一层检查：使用 VirtualFile
        try {
            val virtualFile = LocalFileSystem.getInstance().findFileByPath(absolutePath)
            if (virtualFile != null && virtualFile.exists()) {
                return true
            }
        } catch (e: Exception) {
            // VirtualFile 检查失败，继续用 File IO 检查
        }

        // 兜底检查：使用 File IO
        return try {
            val file = File(absolutePath)
            file.exists()
        } catch (e: Exception) {
            false
        }
    }

    fun createFile(filePath: String, project: Project? = null): File? {
        val cleanPath = normalizeAbsolutePath(filePath, project) ?: return null
        return try {
            val file = File(cleanPath)
            if (!file.exists()) {
                file.parentFile?.mkdirs() // 确保父目录存在
                file.createNewFile()
            }
            file
        } catch (e: IOException) {
            null
        }
    }

    /**
     * 打印当前系统时间和时区信息
     */
    fun printCurrentTimeAndTimezone() {
        val currentTime = java.time.LocalDateTime.now()
        val zoneId = java.time.ZoneId.systemDefault()
        val formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

        println("当前系统时间: ${currentTime.format(formatter)}")
        println("当前时区: ${zoneId.id}")
        println("时区偏移量: ${zoneId.rules.getOffset(java.time.Instant.now())}")
    }

    /**
     * 打印一个随机数
     * @param min 最小值（包含），默认为 1
     * @param max 最大值（包含），默认为 500
     */
    fun printRandomNumber(min: Int = 1, max: Int = 500) {
        val randomNumber = Random.nextInt(min, max + 1)
        val message = "随机数: $randomNumber (范围: $min - $max)"
        println(message)
        logger.info(message)
    }
}