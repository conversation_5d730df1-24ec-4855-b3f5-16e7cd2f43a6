package com.think1024.tocodesign.ideaplugin.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.ui.JBColor
import com.intellij.util.messages.Topic
import com.intellij.util.xmlb.XmlSerializerUtil
import com.think1024.tocodesign.ideaplugin.utils.ColorUtil
import com.think1024.tocodesign.ideaplugin.utils.ConfigUtil
import kotlin.properties.Delegates
import java.util.UUID

/**
 * Persistent application-level settings for the ToCoDesign plugin.
 * This class stores and manages the plugin's global settings across the entire IDE.
 */
@Service(Service.Level.APP)
@State(
    name = "ApplicationPluginSettings",
    storages = [Storage("tocodesign_application_settings.xml")]
)
class ApplicationPluginSettings : PersistentStateComponent<ApplicationPluginSettings> {
    /**
     * The host URL for the plugin's backend services.
     */
    var host: String = if (System.getProperty("idea.debug.mode")=="true") {
        ConfigUtil.getProperty("url.host.test")
    } else {
        ConfigUtil.getProperty("url.host.default")
    }

    /**
     * The host URL for the plugin's frontend services.
     */
    var frontendHost: String = if (System.getProperty("idea.debug.mode")=="true") {
        ConfigUtil.getProperty("url.frontend.host.test")
    } else {
        ConfigUtil.getProperty("url.frontend.host.default")
    }

    /**
     * The previous host URL used before the current one.
     */
    var previousHost: String? = null
    var previousFrontendHost: String? = null

    /**
     * Indicates whether the user is currently logged in.
     */
    var loggedIn: Boolean = false

    /**
     * The username of the logged-in user.
     */
    var username: String = ""

    /**
     * The nickname of the logged-in user.
     */
    var nickname: String = ""

    var preferredUsername: String = ""

    var isPm: String = ""

    var avatar: String = ""

    /**
     * Stored cookies for maintaining user session.
     */
    var cookies: String = ""

    /**
     * Preferred language setting for the plugin UI.
     */
    var language: String = "System Default"

    /**
     * Keyboard shortcut for locating the application.
     */
    var locateAppShortcut: String = ""

    /**
     * Flag to enable automatic updates for the plugin.
     */
    var enableAutoUpdate: Boolean = false

    /**
     * Flag to show bubble messages.
     */
    var showBubbleMessages: Boolean = true

    var inlineCodeCompletion: Boolean = false

    var intelliMerge: Boolean = true
    var intelliMergeDebug: Boolean = getIsDev()

    var onlineWeb: Boolean = true

    /**
     * Stores the highlight background color as an RGB string.
     * This property is persisted and used for serialization.
     */
    var highlightBackgroundColorValue: String = ColorUtil.colorToString(JBColor.LIGHT_GRAY)

    /**
     * The unique device identifier for this IDE instance.
     * This is randomly generated when the plugin is first installed and persisted.
     */
    var deviceId: String = UUID.randomUUID().toString()

    /**
     * Returns the current state of the settings.
     * @return The current ApplicationPluginSettings instance.
     */
    override fun getState(): ApplicationPluginSettings = this

    /**
     * Loads the state from the given settings object.
     * @param state The ApplicationPluginSettings object to load from.
     */
    override fun loadState(state: ApplicationPluginSettings) {
        XmlSerializerUtil.copyBean(state, this)
    }

    fun getIsDev(): Boolean {
        return System.getProperty("idea.debug.mode") == "true"
    }

    companion object {
        /**
         * Gets the singleton instance of ApplicationPluginSettings.
         * @return The ApplicationPluginSettings instance for the entire application.
         */
        fun getInstance(): ApplicationPluginSettings =
            ApplicationManager.getApplication().getService(ApplicationPluginSettings::class.java)

        /**
         * Topic used to subscribe to user ID change events.
         */
        val USER_ID_CHANGED_TOPIC = Topic.create("User ID Change", UserIdChangeListener::class.java)
    }

    /**
     * Listener interface for handling changes in user ID.
     */
    interface UserIdChangeListener {
        /**
         * Called when the user ID has changed.
         *
         * @param newUserId The new user ID, or null if the user ID is unset.
         */
        fun userIdChanged(newUserId: String?)
    }

    /**
     * The userId of the logged-in user.
     * This property is observable and will notify listeners when changed.
     */
    var userId: String by Delegates.observable("") { _, oldValue, newValue ->
        if (oldValue != newValue) {
            ApplicationManager.getApplication().messageBus.syncPublisher(USER_ID_CHANGED_TOPIC).userIdChanged(newValue)
        }
    }
}
