# Notification group names
# These define the categories for different types of notifications in Chinese
notification.group.toco=Toco通知

# Settings dialog titles and tabs
# Labels for the settings dialog and its tabs in Chinese
settings.title=Toco Design 设置
settings.tab.application=应用设置
settings.tab.project=项目设置
settings.tab.indexing=索引设置

# Account related labels
# Text for account-related UI elements in Chinese
account.title=账户
account.not.logged.in=未登录
account.login=登录
account.logout=登出

# Indexing setting panel
indexing.codebase.title=代码索引
indexing.codebase.labelSuccess=个文件已同步
indexing.codebase.button.start=开始索引
indexing.codebase.button.sync=重新索引
indexing.codebase.button.delete=删除索引
indexing.codebase.button.pause=暂停索引
indexing.codebase.button.continue=继续索引
indexing.codebase.tip=代码索引分析您的项目结构并创建智能索引，帮助您快速查找代码、理解依赖关系并提高开发效率。您的代码仅用于索引计算，不会在服务器上永久存储。索引数据存储在服务端，但您的源代码不会被保存。所有处理都遵循严格的隐私标准。

# General settings
# Labels for general application settings in Chinese
general.settings.title=常规设置
host.label=主机：
frontend.host.label=前端主机：
language.label=显示语言：
inline.completion.label=代码补全
intelli.merge.label=智能合并
intelli.merge.debug.label=智能合并调试
online.web.label=在线页面
locate.app.shortcut.label=定位应用快捷键：
locate.app.shortcut.placeholder=按下所需的按键组合

# Privacy and legal
# Labels for privacy and legal information in Chinese
privacy.statement.title=隐私声明
service.agreement.label=服务协议
privacy.policy.label=隐私政策

# Button labels
button.clear=清除
button.save=保存
button.cancel=取消
button.ok=确定
button.no=否
button.create=新建

# Error messages
# Messages displayed when errors occur in Chinese
message.error.host.empty=主机不能为空
message.error.host.invalid=无效的主机 URL
message.error.host.unreachable=主机值配置有误，或当前登录地址无法访问。

# Informational messages
# Messages providing information to the user in Chinese
message.language.change.restart=语言设置已更改。请重新打开设置页面以使更改生效。
title.settings.changed=设置已更改
message.settings.not.applied=设置尚未应用。您想立即应用它们吗？

message.editor.modified.confirm.title=当前标签已修改
message.project.modified.confirm.title=部分标签已修改
message.editor.modified.confirm.message=您确定要关闭吗？

# Login dialog
# Title for the login dialog in Chinese
login.dialog.title=登录

# Notifications
# Messages for various notification scenarios in Chinese
notification.login.failed=登录失败，请重试
notification.logout.failed=登出失败，请重试
notification.login.expired.title=TocoDesign 登录已过期
notification.login.expired=您的登录已过期，请重新登录。

# Actions
# Labels for action items in Chinese
action.open.settings=打开设置

# Locator-related messages
# Messages and titles related to the locator functionality in Chinese
locator.start.fail.title=定位器启动失败
locator.start.fail.message.project.info=项目信息缺失。无法启动定位器。
locator.start.fail.message.socket=初始化 Socket 连接失败。请稍后再试。
locator.title=Toco 定位器
locator.login.required=请在定位之前登录。
locator.activate.online=激活在线状态
locator.offline.message=客户端当前处于离线状态。点击激活在线状态。
locator.no.desktop.app=请打开并登录 TocoDesign 桌面应用程序，然后打开项目：{0}
locator.invalid.message.body=无效的定位请求消息：缺少定位信息
locator.unsupported.type=无效的定位请求消息：定位类型不合法 {0}
locator.unsupported.sub.type=无效的定位请求消息：定位子类型不合法 {0}
locator.missing.field=无效的定位请求消息：定位信息中缺少必填字段值
locator.failed.to.open=定位失败：可能是代码未生成、远程代码未同步或Maven模块未加载。
locator.success=定位成功，请查看IDE
open.tocodesign.tab=在 TocoDesign 标签中打开
locator.file.not.found=定位代码：项目中找不到文件。
locator.cannot.open.file=定位代码：无法在编辑器中打开指定文件。
locator.partial.failure=定位代码：行高亮部分失败: {0}。
locator.highlight.success=定位代码：行高亮成功。
locator.settings.title=定位器设置
highlight.background.color=高亮背景颜色：
choose.highlight.color=选择高亮颜色
reset.color=重置为默认
highlight.color.instruction=点击按钮修改高亮颜色

# Project configuration related messages
# Labels and messages for project configuration in Chinese
button.check=重新检查项目
project.id.label=项目 ID：
project.name.label=项目名称：
error.config.not.found=未找到配置文件。
error.invalid.config=无效的配置。缺少项目 ID 或名称。
success.config.updated=配置更新成功

# Notification messages for configuration-related issues in Chinese
notification.error.project.root.not.found.title=未找到项目根目录
notification.error.project.root.not.found.message=无法确定项目根目录
notification.error.config.not.found.title=未找到配置
notification.error.config.not.found.message=无法找到TocoDesign项目文件：{0}
notification.warning.config.read.error.title=配置错误
notification.warning.config.read.error.message=读取TocoDesign项目文件时出错：{0}

# Project configuration description in Chinese
project.config.description=TocoDesign工程根目录必须有'project'配置文件，包含id和name。

# Status bar widget and menu items
# Labels and messages for the status bar widget and its context menu in Chinese
statusbar.widget.display.name=Toco Design
statusbar.menu.title=Toco Design
statusbar.widget.tooltip=Toco Design 状态
statusbar.menu.open.setting=打开设置
statusbar.menu.login.status.logged_in=已登录（{0}）
statusbar.menu.login.status.not_logged_in=未登录
statusbar.menu.locator.status=定位器连接：{0}
statusbar.menu.locator.status.disconnected=未连接
statusbar.menu.locator.status.connected=未配对(已连接)
statusbar.menu.locator.status.paired=已配对

# Notification messages for JCef-related issues
notification.jcef.unavailable.title=JCef 不可用
notification.jcef.unavailable.content=当前版本的 IntelliJ IDEA 不支持 JCef。请升级到更新的版本

# JDK compatibility check related labels
jdk.version.label=JDK 兼容版本：
button.check.jdk.compatibility=检查
error.jdk.version.empty=请输入 JDK 版本。
success.jdk.compatibility=未发现不兼容项。该项目与指定的 JDK 版本兼容。
dialog.incompatible.items.title=发现不兼容项
error.title=错误
success.title=成功
jdk.version.unsupported.message=输入的JDK版本不受支持。支持检查的JDK版本号范围为[1.8, 21]。
jdk.version.unsupported.title=不支持的JDK版本

# Dialog related labels
dialog.button.close=关闭

# Key for toggling the display of bubble messages
toggle.bubble.messages.current.project=当前项目中显示气泡消息（切换立即生效）
toggle.bubble.messages=显示气泡消息

# New Project Wizard
new.project.wizard.project.name.empty=项目名称为空
new.project.wizard.location.empty=项目路径为空
new.project.wizard.group.name=分组名
new.project.wizard.database=数据库
new.project.wizard.org=组织
new.project.wizard.org.loading=正在加载...
new.project.wizard.org.load.failed=加载组织失败
new.project.wizard.description=描述
new.project.wizard.git.repo=Git仓库
new.project.wizard.database.empty=数据库不能为空
new.project.wizard.jdk.empty=JDK不能为空
new.project.wizard.jdk.goe.11=JDK版本必须大于等于11
new.project.wizard.group.name.empty=分组名不能为空
new.project.wizard.create.project.failed=新建项目失败
new.project.wizard.generate.file=正在生成工程文件

# code generate
code.generate.start=生成代码
code.generate.success=生成模块{0}代码成功
code.generate.failed=生成模块{0}代码失败
code.generate.failed2=生成代码失败
code.generate.cancelled=取消生成模块{0}代码
code.generate.resolve.loading=正在加载...
code.generate.resolve.conflict=解决冲突
code.generate.resolve.conflict.title=冲突
code.generate.resolve.conflict.description=请解决下列文件的冲突
code.generate.resolve.recover.button=恢复
code.generate.resolve.recover=恢复文件
code.generate.resolve.recover.title=可恢复
code.generate.resolve.recover.description=下列文件可恢复
code.generate.resolve.intelli.merge=确认智能合并结果
code.generate.resolve.intelli.merge.detail=详情
code.generate.resolve.intelli.merge.confirm=智能合并{0}个文件
code.generate.recover.do.not.show=当前模块不再显示
code.generate.ours=本地代码
code.generate.theirs=生成的代码
code.generate.base=共同祖先
code.generate.fetching.code=获取模块代码
code.generate.module.config=获取模块配置
code.generate.create.module=创建模块
code.generate.generating=生成代码
code.generate.clean.module=清理模块代码
code.generate.write.module.code=写入模块代码
code.generate.commit.module.code=提交模块代码
code.generate.merge.module.code=合并模块代码
code.generate.diff.module.code=比较模块代码
code.generate.recover.title=存在被删除的文件
code.generate.recover.message=是否需要恢复文件？
code.apply=应用代码
code.apply.all.finished=应用代码完毕
code.apply.failed=应用代码失败
code.apply.failed.file.not.exist=文件不存在
code.apply.accept.all=应用全部
code.apply.accept.all.description=应用所有变更
code.apply.reject.all=拒绝全部
code.apply.reject.all.description=拒绝所有变更
code.diff.current.version = 当前版本
code.diff.origin.version = 原始版本
code.diff.title.review.diff = Review变更

# toco file copy url
copy.file.url.success=复制toco文件url
copy.file.url.success.description=复制toco文件url成功