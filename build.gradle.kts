import BuildUtils.convertMarkdownToHtml
import BuildUtils.getLocalizedMarkdownContent
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import java.util.*

// Define the plugins used in this project
plugins {
    id("java")                                       // Adds Java compilation support
    id("org.jetbrains.kotlin.jvm") version "2.0.0" // 使用支持 K2 的 Kotlin 版本
    id("org.jetbrains.intellij.platform") version "2.6.0"
    id("org.jetbrains.intellij.platform.migration") version "2.6.0"
}

// Configure repositories for dependency resolution
repositories {
    mavenCentral()  // Use Maven Central repository for downloading dependencies
    intellijPlatform{
        defaultRepositories()
    }
}

val luceneVersion = "9.12.1"
// Define project dependencies
// These are the external libraries that the project depends on
dependencies {
    // JetBrains annotations for better code analysis
    // Provides a set of annotations to enhance code inspections and IDE support
    implementation("org.jetbrains:annotations:20.1.0")

    implementation("org.json:json:20090211")

    // OkHttp client for efficient HTTP requests
    // A powerful HTTP client for making network requests with features like connection pooling
    implementation("com.squareup.okhttp3:okhttp:4.10.0")

    // CommonMark library for Markdown processing
    // Provides parsing and rendering capabilities for Markdown content
    implementation("org.commonmark:commonmark:0.20.0")

    // Kotlin standard library
    // Contains essential components for Kotlin development
    implementation(kotlin("stdlib", "2.0.0"))

    // Kotlin reflection library
    // Enables runtime introspection of Kotlin code
    implementation(kotlin("reflect", "2.0.0"))

    // Kotlin coroutines core library
    // Provides support for asynchronous programming with coroutines
//    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    // 不直接引用coroutines，改为使用idea的coroutines
    // 直接引用会导致TocoInlineCompletionProvider的flow出现ClassCastException
    // ClassCastException是因为插件引用的coroutines和idea的coroutines版本不一致导致的
    compileOnly("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0")

    // Apache Lucene for BM25 search
    implementation("org.apache.lucene:lucene-core:$luceneVersion")
    implementation("org.apache.lucene:lucene-queryparser:$luceneVersion")
    implementation("org.apache.lucene:lucene-analysis-common:$luceneVersion")
    implementation("org.apache.lucene:lucene-codecs:$luceneVersion")
    implementation("org.apache.lucene:lucene-backward-codecs:$luceneVersion")

    implementation("org.lz4:lz4-java:1.8.0")
    implementation("net.lingala.zip4j:zip4j:2.11.5")

    // IntelliMerge deps
    implementation("org.jgrapht:jgrapht-core:1.3.0")
    implementation("org.jgrapht:jgrapht-io:1.3.0")
    implementation("com.github.javaparser:javaparser-core:3.12.0")
    implementation("com.github.javaparser:javaparser-symbol-solver-core:3.12.0")
    implementation("org.eclipse.jgit:org.eclipse.jgit:5.2.0.201812061821-r")
    implementation(fileTree(mapOf("dir" to "lib", "include" to listOf("*.jar"))))

    intellijPlatform {
        create("IC", "2024.3")
        bundledPlugins(
            "com.intellij.java",
            "org.jetbrains.kotlin",
            "org.jetbrains.idea.maven"
        )
    }
}

// Read the config.properties file to get project version
val configPropsFile = file("src/main/resources/config.properties")
val configProps = Properties()

if (configPropsFile.canRead()) {
    configProps.load(configPropsFile.inputStream())
} else {
    throw GradleException("Could not read config.properties!")
}

// Extract the project version and name from the properties
val projectVersion: String = configProps.getProperty("project.version")
val projectName: String = configProps.getProperty("project.name")
val projectPluginId: String = configProps.getProperty("project.plugin.id")
val projectGroupId: String = configProps.getProperty("project.group.id")

// Define project group and version
group = projectGroupId
version = projectVersion

intellijPlatform {
    buildSearchableOptions = false

    pluginConfiguration {
        name = configProps.getProperty("project.name")
        version = projectVersion


        val descriptionContent = getLocalizedMarkdownContent("description", "descriptions")
        description = convertMarkdownToHtml(descriptionContent)

        val changeNotesContent = getLocalizedMarkdownContent("change-notes", "changenotes")
        changeNotes = convertMarkdownToHtml(changeNotesContent)

        ideaVersion {
            sinceBuild = "243.0"
            untilBuild = "252.*"
        }
    }

    signing {
        certificateChain = System.getenv("CERTIFICATE_CHAIN")
        privateKey = System.getenv("PRIVATE_KEY")
        password = System.getenv("PRIVATE_KEY_PASSWORD")
    }

    publishing {
        host = "https://vs-env.byteawake.com"
        token = System.getenv("PUBLISH_TOKEN")
        // The pluginVersion is based on the SemVer (https://semver.org) and supports pre-release labels, like 2.1.7-alpha.3
        // Specify pre-release label to publish the plugin in a custom Release Channel automatically. Read more:
        // https://plugins.jetbrains.com/docs/intellij/deployment.html#specifying-a-release-channel
        channels = listOf(getPublishChannel(projectVersion))
    }

    pluginVerification {
        ides {
            recommended()
        }
    }
}

// Configure the source directories
sourceSets {
    main {
        kotlin.srcDirs("src/main/kotlin")        // Kotlin source directory
        resources {
            srcDirs("src/main/resources", "bin")        // Resources directory
        }
    }
}

tasks {
    // Set the JVM compatibility versions for Java compilation
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }

    // Set the JVM compatibility versions for Kotlin compilation
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_17
            // 明确使用 K2 编译器
            languageVersion = KotlinVersion.KOTLIN_2_0
            apiVersion = KotlinVersion.KOTLIN_2_0
            // 禁用特定警告
            freeCompilerArgs.addAll(listOf(
                "-Xskip-prerelease-check",
            ))
        }
    }

    // Configure IDE run task
    // This sets JVM arguments for running the plugin in a development instance of IntelliJ IDEA
    runIde {
        jvmArgs = listOf("-Xmx4G")               // Set maximum heap size to 4GB
        systemProperty("kotlin.compiler.mode", "k2")
        systemProperty("idea.auto.reload.plugins", "true")
        systemProperty("idea.debug.mode", "true")
        systemProperty("ide.browser.jcef.log.level", "error")
    }

    // Configure test execution
    // This specifies to use JUnit Platform for running tests
    test {
        useJUnitPlatform()
    }

    // Handle resource processing
    // This ensures that duplicate resources are excluded during the build process
    processResources {
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }

    // 配置插件构建任务
    buildPlugin {
        // 确保包含 META-INF 目录
        from("src/main/resources/META-INF") {
            into("META-INF")
        }

        archiveFileName.set("${projectName}-${projectVersion}.zip")  // Set the output file name
    }
}

// Helper function to determine the publishing channel based on the version string
fun getPublishChannel(version: String): String {
    return when {
        version.contains("beta") -> "beta"
        else -> "stable"
    }
}
